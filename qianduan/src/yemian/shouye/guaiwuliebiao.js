import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { meiti_chaxun } from '../../gongju/shebeishiPei_gongju.js';
import { wangguanpeizhi } from '../../peizhi/wangguanpeizhi.js';

// 怪物列表容器
const Guaiwuliebiaorongqi = styled.div`
  width: 100%;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}15, ${props.theme.yanse.danjinse_qian}10)`
    : props.theme.yanse.biaomian};
  border: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}40`
    : props.theme.yanse.biankuang};
  border-radius: 15px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? `0 4px 12px ${props.theme.yanse.danjinse}20, ${props.theme.yinying.xiao}`
    : props.theme.yinying.xiao};

  ${meiti_chaxun.shouji} {
    border-radius: 12px;
    margin-bottom: 15px;
    margin-left: 0;
    margin-right: 0;
    max-width: 100%;
  }

  ${meiti_chaxun.pingban} {
    border-radius: 12px;
    margin-bottom: 15px;
    max-width: 100%;
  }
`;

// 标题区域
const Biaotiquyu = styled.div`
  padding: 16px 20px;
  border-bottom: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}30`
    : props.theme.yanse.biankuang};
  display: flex;
  align-items: center;
  gap: 12px;

  ${meiti_chaxun.shouji} {
    padding: 12px 16px;
    gap: 8px;
  }

  ${meiti_chaxun.pingban} {
    padding: 14px 18px;
    gap: 10px;
  }
`;

// 怪物数据图标
const Guaiwutubiao = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}80, ${props.theme.yanse.danjinse_qian}60)`
    : `linear-gradient(135deg, ${props.theme.yanse.zhuyao}80, ${props.theme.yanse.zhuyao}60)`};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  flex-shrink: 0;

  ${meiti_chaxun.shouji} {
    width: 28px;
    height: 28px;
    font-size: 14px;
    border-radius: 6px;
  }

  ${meiti_chaxun.pingban} {
    width: 30px;
    height: 30px;
    font-size: 15px;
    border-radius: 7px;
  }
`;

// 标题文字
const Biaotiwenzi = styled.h3`
  margin: 0;
  font-size: ${props => props.theme.ziti.daxiao.da};
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  flex: 1;

  ${meiti_chaxun.shouji} {
    font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  }

  ${meiti_chaxun.pingban} {
    font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  }
`;

// 怪物列表滚动容器
const Guaiwugundongrongqi = styled.div`
  padding: 16px 20px;
  overflow-x: auto;
  overflow-y: hidden;

  /* 隐藏滚动条但保留滚动功能 */
  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;

  ${meiti_chaxun.shouji} {
    padding: 12px 16px;
  }

  ${meiti_chaxun.pingban} {
    padding: 14px 18px;
  }
`;

// 怪物列表容器
const Guaiwuliebiaoliebiao = styled.div`
  display: flex;
  gap: 16px;
  min-width: fit-content;

  ${meiti_chaxun.shouji} {
    gap: 12px;
  }

  ${meiti_chaxun.pingban} {
    gap: 14px;
  }
`;

// 怪物卡片
const Guaiwukapian = styled(motion.div)`
  min-width: 200px;
  width: 200px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}10, ${props.theme.yanse.danjinse_qian}05)`
    : 'rgba(255, 255, 255, 0.8)'};
  border: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}30`
    : props.theme.yanse.biankuang};
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  flex-shrink: 0;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.mingcheng === 'anhei'
      ? `0 8px 20px ${props.theme.yanse.danjinse}30, ${props.theme.yinying.zhongdeng}`
      : props.theme.yinying.zhongdeng};
    border-color: ${props => props.theme.mingcheng === 'anhei'
      ? `${props.theme.yanse.danjinse}60`
      : props.theme.yanse.zhuyao};
  }

  ${meiti_chaxun.shouji} {
    min-width: 140px;
    width: 140px;
    padding: 10px;
    border-radius: 10px;

    &:hover {
      transform: none;
    }
  }

  ${meiti_chaxun.pingban} {
    min-width: 160px;
    width: 160px;
    padding: 12px;
    border-radius: 11px;
  }
`;

// 怪物头像
const Guaiwutouxiang = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 10px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}20, ${props.theme.yanse.danjinse_qian}10)`
    : `linear-gradient(135deg, ${props.theme.yanse.zhuyao}20, ${props.theme.yanse.zhuyao}10)`};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  margin: 0 auto 12px auto;
  border: 2px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}40`
    : props.theme.yanse.biankuang};

  ${meiti_chaxun.shouji} {
    width: 40px;
    height: 40px;
    font-size: 18px;
    margin-bottom: 8px;
    border-radius: 8px;
    border-width: 1px;
  }

  ${meiti_chaxun.pingban} {
    width: 50px;
    height: 50px;
    font-size: 20px;
    margin-bottom: 10px;
    border-radius: 9px;
  }
`;

// 怪物名称
const Guaiwumingcheng = styled.div`
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  font-weight: ${props => props.theme.ziti.zhongliang.zhongdeng};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  text-align: center;
  margin-bottom: 8px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  ${meiti_chaxun.shouji} {
    font-size: ${props => props.theme.ziti.daxiao.xiao};
    margin-bottom: 6px;
  }

  ${meiti_chaxun.pingban} {
    font-size: ${props => props.theme.ziti.daxiao.xiao};
    margin-bottom: 7px;
  }
`;

// 怪物等级
const Guaiwudengji = styled.div`
  font-size: ${props => props.theme.ziti.daxiao.xiao};
  color: ${props => props.theme.yanse.wenzi_ciyao};
  text-align: center;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}20`
    : props.theme.yanse.beijing_qian};
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: ${props => props.theme.ziti.zhongliang.zhongdeng};

  ${meiti_chaxun.shouji} {
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
    padding: 3px 6px;
    border-radius: 4px;
  }

  ${meiti_chaxun.pingban} {
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
    padding: 3px 7px;
    border-radius: 5px;
  }
`;

// 加载状态
const Jiazaizhuangtai = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: ${props => props.theme.yanse.wenzi_ciyao};
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};

  ${meiti_chaxun.shouji} {
    padding: 30px 16px;
    font-size: ${props => props.theme.ziti.daxiao.xiao};
  }
`;

// 错误状态
const Cuowuzhuangtai = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: ${props => props.theme.yanse.cuowu};
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};

  ${meiti_chaxun.shouji} {
    padding: 30px 16px;
    font-size: ${props => props.theme.ziti.daxiao.xiao};
  }
`;

// 怪物列表组件
function Guaiwuliebiao() {
  const [guaiwushuju, shezhi_guaiwushuju] = useState([]);
  const [jiazaizhong, shezhi_jiazaizhong] = useState(true);
  const [cuowu, shezhi_cuowu] = useState(null);

  // 模拟怪物数据（临时使用）
  const moniGuaiwushuju = [
    { guaiwu_id: 1001, guaiwu_mingcheng: '波利', level: 1, yuansu: ['无'], zhongzu: ['植物'] },
    { guaiwu_id: 1002, guaiwu_mingcheng: '小强', level: 2, yuansu: ['地'], zhongzu: ['昆虫'] },
    { guaiwu_id: 1003, guaiwu_mingcheng: '蘑菇宝贝', level: 3, yuansu: ['地'], zhongzu: ['植物'] },
    { guaiwu_id: 1004, guaiwu_mingcheng: '红蝙蝠', level: 5, yuansu: ['暗'], zhongzu: ['动物'] },
    { guaiwu_id: 1005, guaiwu_mingcheng: '绿水母', level: 4, yuansu: ['水'], zhongzu: ['鱼贝'] },
    { guaiwu_id: 1006, guaiwu_mingcheng: '小鸡', level: 1, yuansu: ['无'], zhongzu: ['动物'] },
    { guaiwu_id: 1007, guaiwu_mingcheng: '蚯蚓', level: 2, yuansu: ['地'], zhongzu: ['昆虫'] },
    { guaiwu_id: 1008, guaiwu_mingcheng: '树精', level: 6, yuansu: ['地'], zhongzu: ['植物'] }
  ];

  // 获取怪物头像表情符号
  const huoquguaiwubiaoqing = (mingcheng) => {
    const biaoqingying = {
      '波利': '🟢',
      '小强': '🪲',
      '蘑菇宝贝': '🍄',
      '红蝙蝠': '🦇',
      '绿水母': '🪼',
      '小鸡': '🐣',
      '蚯蚓': '🪱',
      '树精': '🌳'
    };
    return biaoqingying[mingcheng] || '👾';
  };

  // 组件挂载时加载数据
  useEffect(() => {
    const jiazaiguaiwushuju = async () => {
      try {
        shezhi_jiazaizhong(true);
        shezhi_cuowu(null);
        
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 暂时使用模拟数据
        shezhi_guaiwushuju(moniGuaiwushuju);
        
      } catch (error) {
        console.error('加载怪物数据失败:', error);
        shezhi_cuowu('加载怪物数据失败，请稍后重试');
      } finally {
        shezhi_jiazaizhong(false);
      }
    };

    jiazaiguaiwushuju();
  }, []);

  // 处理怪物卡片点击
  const chulidianjikapian = (guaiwu) => {
    console.log('点击怪物:', guaiwu);
    // 这里可以添加跳转到怪物详情页的逻辑
  };

  return (
    <Guaiwuliebiaorongqi>
      {/* 标题区域 */}
      <Biaotiquyu>
        <Guaiwutubiao>👾</Guaiwutubiao>
        <Biaotiwenzi>怪物数据</Biaotiwenzi>
      </Biaotiquyu>

      {/* 内容区域 */}
      {jiazaizhong ? (
        <Jiazaizhuangtai>正在加载怪物数据...</Jiazaizhuangtai>
      ) : cuowu ? (
        <Cuowuzhuangtai>{cuowu}</Cuowuzhuangtai>
      ) : (
        <Guaiwugundongrongqi>
          <Guaiwuliebiaoliebiao>
            <AnimatePresence>
              {guaiwushuju.map((guaiwu, suoyin) => (
                <Guaiwukapian
                  key={guaiwu.guaiwu_id}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3, delay: suoyin * 0.1 }}
                  onClick={() => chulidianjikapian(guaiwu)}
                >
                  <Guaiwutouxiang>
                    {huoquguaiwubiaoqing(guaiwu.guaiwu_mingcheng)}
                  </Guaiwutouxiang>
                  <Guaiwumingcheng>{guaiwu.guaiwu_mingcheng}</Guaiwumingcheng>
                  <Guaiwudengji>Lv.{guaiwu.level}</Guaiwudengji>
                </Guaiwukapian>
              ))}
            </AnimatePresence>
          </Guaiwuliebiaoliebiao>
        </Guaiwugundongrongqi>
      )}
    </Guaiwuliebiaorongqi>
  );
}

export default Guaiwuliebiao;
